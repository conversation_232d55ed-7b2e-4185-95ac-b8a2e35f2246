-- Add INSERT and UPDATE policies for subscriptions table to allow trial creation
-- Users can create and update trial subscriptions for accounts they have access to

-- INSERT(subscriptions):
-- Users can create trial subscriptions on an account they are a member of
create policy subscriptions_insert_trial on public.subscriptions for
insert
  to authenticated with check (
    (
      has_role_on_account (account_id)
      and public.is_set ('enable_team_account_billing')
      and status = 'trialing'
    )
    or (
      account_id = (
        select
          auth.uid ()
      )
      and public.is_set ('enable_account_billing')
      and status = 'trialing'
    )
  );

-- UPDATE(subscriptions):
-- Users can update trial subscriptions on an account they are a member of
create policy subscriptions_update_trial on public.subscriptions for
update
  to authenticated using (
    (
      has_role_on_account (account_id)
      and public.is_set ('enable_team_account_billing')
    )
    or (
      account_id = (
        select
          auth.uid ()
      )
      and public.is_set ('enable_account_billing')
    )
  )
  with check (
    (
      has_role_on_account (account_id)
      and public.is_set ('enable_team_account_billing')
    )
    or (
      account_id = (
        select
          auth.uid ()
      )
      and public.is_set ('enable_account_billing')
    )
  ); 