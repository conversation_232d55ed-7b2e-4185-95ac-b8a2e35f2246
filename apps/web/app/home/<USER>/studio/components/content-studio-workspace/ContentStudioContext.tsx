// Re-export from context/ContentStudioContext.tsx
// This file serves as the main entry point for all context-related exports

export { 
  ContentStudioProvider,
  useContentStudio,
  useBaseContent,
  useTextContent,
  useImageContent,
  useEditorContent,
  useVideoContent
} from './context/ContentStudioContext';

// Re-export types for convenience
export type {
  ContentType,
  ImageOptionType,
  ImageGenerationOptions,
  VisualDescriptionGroup,
  QualityData,
  BaseContentContextState,
  TextContentContextState,
  ImageContentContextState,
  EditorContentContextState,
  // VideoContentContextState
} from './context/types';

// Re-export video context types
export type {
  VideoOption,
  VideoContentContextState
} from './context/VideoContext';