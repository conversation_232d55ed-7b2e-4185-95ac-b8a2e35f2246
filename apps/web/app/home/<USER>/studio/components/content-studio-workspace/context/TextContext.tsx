import React, { createContext, useContext, useState, ReactNode } from 'react';
import { TextContentContextState } from './types';

// Create the text content context
const TextContentContext = createContext<TextContentContextState | undefined>(undefined);

// Provider component for text content context
export function TextContentProvider({ children }: { children: ReactNode }) {
  const [textContent, setTextContent] = useState<string | null>(null);
  const [input, setInput] = useState('');
  
  const value = {
    textContent,
    setTextContent,
    input,
    setInput
  };

  return (
    <TextContentContext.Provider value={value}>
      {children}
    </TextContentContext.Provider>
  );
}

// Custom hook for accessing the text content context
export function useTextContent() {
  const context = useContext(TextContentContext);
  if (context === undefined) {
    throw new Error('useTextContent must be used within a TextContentProvider');
  }
  return context;
}