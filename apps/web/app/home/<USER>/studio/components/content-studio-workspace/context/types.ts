import { Block } from "@blocknote/core";
import { CompanyContent } from "~/types/company-content";
import { BlockNoteEditor } from "@blocknote/core";

// Content type definitions
export type ContentType = 'text' | 'image' | 'video';

// Image option type used in ImageSubmenu
export type ImageOptionType = 'ai' | 'stock' | 'upload' | 'existing' | 'selected';

// Image generation option types
export interface ImageGenerationOptions {
  image_styles?: "Realism / Photorealism" | "Surrealism" | "Abstract" | "Minimalism" | "Impressionism" | 
    "Expressionism" | "Pop Art" | "Vaporwave" | "Cyberpunk" | "Fantasy" | "Anime/Manga" | 
    "Watercolor Painting" | "Oil Painting" | "Sketch" | "Cartoon";
  
  image_modes?: "Photo Mode / Photorealistic" | "Digital Art / Illustration" | "Concept Art" | 
    "3D Render" | "Cinematic" | "HDR (High Dynamic Range)" | "Low-poly";
  
  image_lighting?: "Natural Light" | "Ambient Light" | "Soft Light" | "Harsh Light" | "Backlit" | 
    "Side-lit" | "Rim Lighting" | "Studio Lighting" | "Spotlight" | "Neon Lighting" | 
    "Golden Hour" | "Twilight" | "Moonlight";
  
  image_details?: "Ultra-detailed" | "Highly detailed" | "Intricate" | "Minimal Detail" | "Simplified" | 
    "Hyperrealistic" | "Textured" | "Smooth" | "Grainy" | "Photorealistic Details";
  
  image_color_balance?: "Warm" | "Cool" | "Neutral" | "Vibrant" | "Muted" | "Pastel" | "High Contrast" | 
    "Low Contrast" | "Monochromatic" | "Complementary" | "Analogous" | "Split-Complementary";
  
  image_camera_angle_perspective?: "Eye-level" | "Bird's-eye view" | "Worm's-eye view" | "Macro" | 
    "Wide-angle" | "Close-up";
  
  image_composition_framing?: "Rule of Thirds" | "Golden Ratio" | "Centered" | "Asymmetrical" | 
    "Dynamic framing";
  
  image_environment_setting?: "Indoor" | "Outdoor" | "Urban" | "Rural" | "Futuristic" | "Natural" | 
    "Fantasy" | "Industrial";
  
  image_mood_atmosphere?: "Calm" | "Energetic" | "Mysterious" | "Whimsical" | "Dramatic" | "Serene" | 
    "Nostalgic";
  
  image_special_effects_post_processing?: "Bokeh" | "Depth of Field" | "Motion Blur" | "HDR" | 
    "Film Grain" | "Vignette" | "Color Grading";
  
  image_material_surface_texture?: "Matte" | "Glossy" | "Metallic" | "Rough" | "Smooth" | "Reflective" | 
    "Textured";
  
  artistic_influences_references?: string;
}

// Visual description groups used for AI image generation
export interface VisualDescriptionGroup {
  Balanced: string;
  Subtle: string;
  Learned: string;
  Prominent: string;
  Full: string;
}

// Quality data for content evaluation
export interface QualityData {
  score: number;
  suggestions: string[];
}

// Context interfaces
export interface BaseContentContextState {
  selectedType: ContentType | null;
  setSelectedType: (type: ContentType | null) => void;
  handleInsert: (content: string) => void;
  qualityData: QualityData;
  currentBlocks: Block[] | null;
  setCurrentBlocks: (blocks: Block[] | null) => void;
  isGenerating: boolean;
  setIsGenerating: (generating: boolean) => void;
}

export interface TextContentContextState {
  textContent: string | null;
  setTextContent: (content: string | null) => void;
  input: string;
  setInput: (input: string) => void;
}

export interface ImageContentContextState {
  selectedImageOption: ImageOptionType | null;
  setSelectedImageOption: (option: ImageOptionType | null) => void;
  input: string;
  setInput: (input: string) => void;
  imageOptions: ImageGenerationOptions;
  setImageOptions: (options: ImageGenerationOptions) => void;
  selectedImageUrl: string | null;
  setSelectedImageUrl: (url: string | null) => void;
  selectedImageId: string | null;
  setSelectedImageId: (id: string | null) => void;
  enlargedImageUrl: string | null;
  setEnlargedImageUrl: (url: string | null) => void;
  visualDescriptionGroup: VisualDescriptionGroup;
  setVisualDescriptionGroup: (group: VisualDescriptionGroup) => void;
  imageUrls: string[];
  setImageUrls: (urls: string[]) => void;
  selectedEditorImage: string | null;
  setSelectedEditorImage: (url: string | null) => void;
  generateVisualDescription: (
    input: string, 
    campaignIdea: any, 
    setInput: (input: string) => void, 
    setVisualDescriptionGroup: (group: VisualDescriptionGroup) => void, 
    imageOptions: ImageGenerationOptions,
    companyContentId: any
  ) => Promise<any>;
  generateImages: (
    visualDescriptionGroup: VisualDescriptionGroup, 
    setImageUrls: (urls: string[]) => void, 
    imageOptions: ImageGenerationOptions
  ) => Promise<any>;
}

export interface EditorContentContextState {
  blockContent: Block[] | null | undefined;
  setBlockContent: (content: Block[] | null | undefined) => void;
  editor: BlockNoteEditor | null;
  setEditor: (editor: BlockNoteEditor | null) => void;
}

// Add TypeScript declaration for global window object
declare global {
  interface Window {
    _baseContentContext?: BaseContentContextState;
  }
}
