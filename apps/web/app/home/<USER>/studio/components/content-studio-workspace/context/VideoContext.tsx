import React, { createContext, useContext, ReactNode, useState } from 'react';
import { CompanyContent } from '~/types/company-content';

// Define the types for the video context
export type VideoOption = 'existing' | 'create' | 'upload' | 'selected' | 'generate' | null;

// Define the video editor state interface
export interface VideoEditorState {
  contentId: string | null;
  savedAt: Date | null;
  isSaving: boolean;
  hasUnsavedChanges: boolean;
}

export interface VideoContentContextState {
  selectedVideoOption: VideoOption;
  setSelectedVideoOption: (option: VideoOption) => void;
  selectedEditorVideo: string | null;
  setSelectedEditorVideo: (url: string | null) => void;
  
  // Video editor related state
  videoEditorState: VideoEditorState;
  setVideoEditorContentId: (contentId: string | null) => void;
  setVideoEditorSaving: (isSaving: boolean) => void;
  setVideoEditorSaved: (savedAt: Date | null) => void;
  setVideoEditorHasUnsavedChanges: (hasChanges: boolean) => void;
  
  // Active content
  activeVideoContent: CompanyContent | null;
  setActiveVideoContent: (content: CompanyContent | null) => void;
}

// Create the video content context
const VideoContentContext = createContext<VideoContentContextState | undefined>(undefined);

// Provider component for video content context
export function VideoContentProvider({ children }: { children: ReactNode }) {
  // Video specific state
  const [selectedVideoOption, setSelectedVideoOption] = useState<VideoOption>(null);
  const [selectedEditorVideo, setSelectedEditorVideo] = useState<string | null>(null);
  
  // Video editor state
  const [videoEditorState, setVideoEditorState] = useState<VideoEditorState>({
    contentId: null,
    savedAt: null,
    isSaving: false,
    hasUnsavedChanges: false
  });
  
  // Active video content with memoized setter
  const [activeVideoContent, setActiveVideoContentState] = useState<CompanyContent | null>(null);
  
  // Memoized setter to avoid unnecessary re-renders
  const setActiveVideoContent = React.useCallback((content: CompanyContent | null) => {
    setActiveVideoContentState(prevContent => {
      // Skip update if ids match to prevent circular updates
      if (prevContent?.id === content?.id) return prevContent;
      return content;
    });
  }, []);
  
  // Functions to update video editor state
  // Using useCallback to ensure these functions don't change on every render
  const setVideoEditorContentId = React.useCallback((contentId: string | null) => {
    setVideoEditorState(prev => {
      // Only update if the value actually changed
      if (prev.contentId === contentId) return prev;
      return { ...prev, contentId };
    });
  }, []);
  
  const setVideoEditorSaving = React.useCallback((isSaving: boolean) => {
    setVideoEditorState(prev => {
      // Only update if the value actually changed
      if (prev.isSaving === isSaving) return prev;
      return { ...prev, isSaving };
    });
  }, []);
  
  const setVideoEditorSaved = React.useCallback((savedAt: Date | null) => {
    setVideoEditorState(prev => {
      // Only update if something meaningful changed
      if (prev.savedAt === savedAt && !prev.isSaving && !prev.hasUnsavedChanges) return prev;
      return { 
        ...prev, 
        savedAt, 
        isSaving: false,
        hasUnsavedChanges: false 
      };
    });
  }, []);
  
  const setVideoEditorHasUnsavedChanges = React.useCallback((hasUnsavedChanges: boolean) => {
    setVideoEditorState(prev => {
      // Only update if the value actually changed
      if (prev.hasUnsavedChanges === hasUnsavedChanges) return prev;
      return { ...prev, hasUnsavedChanges };
    });
  }, []);
  
  const value: VideoContentContextState = {
    selectedVideoOption,
    setSelectedVideoOption,
    selectedEditorVideo,
    setSelectedEditorVideo,
    
    // Video editor state
    videoEditorState,
    setVideoEditorContentId,
    setVideoEditorSaving,
    setVideoEditorSaved,
    setVideoEditorHasUnsavedChanges,
    
    // Active content
    activeVideoContent,
    setActiveVideoContent
  };

  return (
    <VideoContentContext.Provider value={value}>
      {children}
    </VideoContentContext.Provider>
  );
}

// Custom hook for accessing the video content context
export function useVideoContent() {
  const context = useContext(VideoContentContext);
  if (context === undefined) {
    throw new Error('useVideoContent must be used within a VideoContentProvider');
  }
  return context;
}