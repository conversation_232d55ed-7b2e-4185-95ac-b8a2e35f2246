import React, { ReactNode } from 'react';
import { BaseContentProvider, useBaseContent } from './BaseContext';
import { TextContentProvider, useTextContent } from './TextContext';
import { ImageContentProvider, useImageContent } from './ImageContext';
import { EditorContentProvider, useEditorContent } from './EditorContext';
import { VideoContentProvider, useVideoContent } from './VideoContext';

// Main provider component that composes all domain contexts
export function ContentStudioProvider({ children }: { children: ReactNode }) {
  return (
    <BaseContentProvider>
      <TextContentProvider>
        <ImageContentProvider>
          <EditorContentProvider>
            <VideoContentProvider>
              {children}
            </VideoContentProvider>
          </EditorContentProvider>
        </ImageContentProvider>
      </TextContentProvider>
    </BaseContentProvider>
  );
}

// Combined hook for convenience when needing all contexts
export function useContentStudio() {
  return {
    ...useBaseContent(),
    ...useTextContent(),
    ...useImageContent(),
    ...useEditorContent(),
    ...useVideoContent()
  };
}

// Re-export individual context hooks for direct imports
export {
  useBaseContent,
  useTextContent,
  useImageContent,
  useEditorContent,
  useVideoContent
};