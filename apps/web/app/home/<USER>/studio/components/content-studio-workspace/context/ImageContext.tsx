import React, { createContext, useContext, useState, ReactNode } from 'react';
import { useTeamAccountWorkspace } from "@kit/team-accounts/hooks/use-team-account-workspace";
import { useBrandData } from "~/hooks/use-brand-data";
import { extractBrandBrief, extractCampaignBrief } from "~/utils/brief.util";
import { updateCompanyContent } from "~/services/company-content";
import { 
  ImageContentContextState, 
  VisualDescriptionGroup, 
  ImageGenerationOptions,
  ImageOptionType
} from './types';
import { useZero } from '~/hooks/use-zero';

// Create the image content context
const ImageContentContext = createContext<ImageContentContextState | undefined>(undefined);

// Provider component for image content context
export function ImageContentProvider({ children }: { children: ReactNode }) {
  const [selectedImageOption, setSelectedImageOption] = useState<ImageOptionType | null>(null);
  const [input, setInput] = useState('');
  const zero = useZero();  
  const [imageOptions, setImageOptions] = useState<ImageGenerationOptions>({});
  const [selectedImageUrl, setSelectedImageUrl] = useState<string | null>(null);
  const [selectedImageId, setSelectedImageId] = useState<string | null>(null);
  const [enlargedImageUrl, setEnlargedImageUrl] = useState<string | null>(null);
  const [visualDescriptionGroup, setVisualDescriptionGroup] = useState<VisualDescriptionGroup>({
    Balanced: '',
    Subtle: '',
    Learned: '',
    Prominent: '',
    Full: ''
  });
  const [imageUrls, setImageUrls] = useState<string[]>([]);
  const [selectedEditorImage, setSelectedEditorImage] = useState<string | null>(null);
  console.log({selectedImageOption});
  // External hooks for API calls
  const workspace = useTeamAccountWorkspace();
  const brand = useBrandData(workspace.account.id);
  
  // API methods for image generation
  const generateVisualDescription = async (
    input: string, 
    // ideaDetails: any, 
    // setInput: (input: string) => void, 
    // setVisualDescriptionGroup: (group: VisualDescriptionGroup) => void, 
    imageOptions: ImageGenerationOptions,
    companyContentId: string
  ) => {
    console.log('getVisualDescription', input, brand.data?.brand);
   
    // Format image generation styles
    const image_gen_styles = formatImageStyles(imageOptions);
    
    try {
      const response = await fetch('/api/ai/generate-idea-visual-description', {
        method: 'POST',
        body: JSON.stringify({ 
          brand_name: workspace.account.name,
          brand_brief: brand.data?.brand ? extractBrandBrief(brand.data.brand) : "No Brand Brief Provided",
          // creative_brief: JSON.stringify(campaignIdea?.brief) || "No Creative Brief Provided",
          // campaign_info: extractCampaignBrief(campaign) || "No Campaign Info Provided",
          product_info: brand.data?.brand.product_list || "No Product Info Provided",
          generated_content: input || "No Generated Content Provided",
          initial_visual_desc: input || "No Initial Visual Description Provided",
          language: "English",
          image_gen_styles
        })
      });
      
      const data = await response.json();
      console.log('generateVisualDescription - data', data);
      // zero.mutate.company_content.update({
      //   id: companyContentId,
      //   values: {
      //     visual_description_group: data.visual_description,
      //   }
      // });
      // if (data.visual_description && companyContentId) {
      //   updateCompanyContent(companyContentId, {
      //     visual_description_group: data.visual_description
      //   });
      // }
      
      setInput(data.visual_description.Balanced);
      setVisualDescriptionGroup(data.visual_description);
      return data;
    } catch (error) {
      console.error('Error generating visual description:', error);
      throw error;
    }
  };

  // Generate images based on visual descriptions
  const generateImages = async (
    visualDescriptionGroup: VisualDescriptionGroup, 
    setImageUrls: React.Dispatch<React.SetStateAction<string[]>>,
    imageOptions: ImageGenerationOptions
  ) => {

    const imagePrompts = Object.values(visualDescriptionGroup);
    
    // Format image generation styles
    const image_gen_styles = formatImageStyles(imageOptions);

    const promises = imagePrompts.map(async (prompt) => {
      if(prompt === "") {
        return;
      }
      
      try {
        console.log('image_gen_styles', process.env.NODE_ENV);
        const imageRoute = process.env.NODE_ENV == 'development' ? '/api/latest/generate-image' : '/api/ai/generate-image'
        const response = await fetch(imageRoute, {
          method: 'POST',
          body: JSON.stringify({
            image_prompt: input + " " + JSON.stringify(image_gen_styles),
            aspect_ratio: "custom",
            company_id: workspace.account.id,
            image_gen_styles
          })
        });
        
        const data = await response.json();
        console.log('imageUrls', data.url);
        setImageUrls(prev => {
          return [...prev, data.url]
        });
        return data.url;
      } catch (error) {
        console.error('Error generating image:', error);
        throw error;
      }
    });
    
    return Promise.all(promises);
  };
  
  // Helper function to format image styles
  const formatImageStyles = (imageOptions: ImageGenerationOptions) => {
    const image_gen_styles: any = {
      artistic_influences_references: imageOptions?.artistic_influences_references || null,
      image_camera_angle_perspective: imageOptions?.image_camera_angle_perspective || undefined,
      image_color_balance: imageOptions?.image_color_balance || undefined,
      image_composition_framing: imageOptions?.image_composition_framing || undefined,
      image_details: imageOptions?.image_details || undefined,
      image_environment_setting: imageOptions?.image_environment_setting || undefined,
      image_lighting: imageOptions?.image_lighting || undefined,
      image_material_surface_texture: imageOptions?.image_material_surface_texture || undefined,
      image_modes: imageOptions?.image_modes || undefined,
      image_mood_atmosphere: imageOptions?.image_mood_atmosphere || undefined,
      image_special_effects_post_processing: imageOptions?.image_special_effects_post_processing || undefined,
      image_styles: imageOptions?.image_styles || undefined,
      main_actions: [],
      main_colors: [],
      main_elements: []
    };

    // Remove any undefined properties
    Object.keys(image_gen_styles).forEach(key => {
      if (image_gen_styles[key] === undefined) {
        delete image_gen_styles[key];
      }
    });
    
    return image_gen_styles;
  };
  
  const value = {
    selectedImageOption,
    setSelectedImageOption,
    input,
    setInput,
    imageOptions,
    setImageOptions,
    selectedImageUrl,
    setSelectedImageUrl,
    selectedImageId,
    setSelectedImageId,
    enlargedImageUrl,
    setEnlargedImageUrl,
    visualDescriptionGroup,
    setVisualDescriptionGroup,
    imageUrls,
    setImageUrls,
    selectedEditorImage,
    setSelectedEditorImage,
    generateVisualDescription,
    generateImages
  };

  return (
    <ImageContentContext.Provider value={value}>
      {children}
    </ImageContentContext.Provider>
  );
}

// Custom hook for accessing the image content context
export function useImageContent() {
  const context = useContext(ImageContentContext);
  if (context === undefined) {
    throw new Error('useImageContent must be used within an ImageContentProvider');
  }
  return context;
}