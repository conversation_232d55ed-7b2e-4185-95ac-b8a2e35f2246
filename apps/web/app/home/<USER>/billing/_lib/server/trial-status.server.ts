'use server';

import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { getLogger } from '@kit/shared/logger';

export interface TrialStatusData {
  accountId: string;
  status: 'inactive' | 'active' | 'expired' | 'converted';
  startedAt: string | null;
  endsAt: string | null;
  daysRemaining: number | null;
  isExpired: boolean;
  isActive: boolean;
  isConverted: boolean;
  isInactive: boolean;
  isPaid: boolean;
  progressPercentage: number;
  timeRemaining: {
    days: number;
    hours: number;
    minutes: number;
  } | null;
}

export async function getTrialStatusFromSubscriptions(accountId: string): Promise<TrialStatusData | null> {
  const logger = await getLogger();
  const client = getSupabaseServerClient();

  try {
    logger.info({ accountId }, 'Getting trial status from subscriptions');

    // Query subscriptions directly to get trial status
    const { data: subscriptions, error: queryError } = await client
      .from('subscriptions')
      .select('*')
      .eq('account_id', accountId)
      .order('created_at', { ascending: false });

    if (queryError) {
      logger.error({ error: queryError, accountId }, 'Failed to query subscriptions');
      return null;
    }

    if (!subscriptions || subscriptions.length === 0) {
      // No subscriptions found, return inactive status
      return {
        accountId,
        status: 'inactive',
        startedAt: null,
        endsAt: null,
        daysRemaining: null,
        isExpired: false,
        isActive: false,
        isConverted: false,
        isInactive: true,
        isPaid: false,
        progressPercentage: 0,
        timeRemaining: null,
      };
    }

    // Find the most recent active subscription
    const activeSubscription = subscriptions.find(sub => sub.active);
    const trialSubscription = subscriptions.find(sub => 
      sub.trial_starts_at && sub.trial_ends_at
    );

    let status: 'inactive' | 'active' | 'expired' | 'converted' = 'inactive';
    let startedAt: string | null = null;
    let endsAt: string | null = null;

    if (activeSubscription) {
      if (activeSubscription.status === 'trialing') {
        status = 'active';
        startedAt = activeSubscription.trial_starts_at;
        endsAt = activeSubscription.trial_ends_at;
      } else if (activeSubscription.status === 'active') {
        // Check if this was originally a trial that got converted
        if (activeSubscription.trial_starts_at) {
          status = 'converted';
          startedAt = activeSubscription.trial_starts_at;
          endsAt = activeSubscription.trial_ends_at;
        } else {
          status = 'converted'; // Direct paid subscription
        }
      }
    } else if (trialSubscription) {
      // Check for expired trials
      const trialEndTime = new Date(trialSubscription.trial_ends_at).getTime();
      const now = Date.now();
      
      if (trialEndTime < now) {
        status = 'expired';
        startedAt = trialSubscription.trial_starts_at;
        endsAt = trialSubscription.trial_ends_at;
      }
    }

    // Calculate time-based properties
    const now = Date.now();
    let daysRemaining: number | null = null;
    let isExpired = false;
    let progressPercentage = 0;
    let timeRemaining: TrialStatusData['timeRemaining'] = null;

    if (endsAt && startedAt) {
      const startTime = new Date(startedAt as string).getTime();
      const endTime = new Date(endsAt as string).getTime();
      const totalDuration = endTime - startTime;
      const elapsed = now - startTime;
      const remaining = endTime - now;

      // Calculate days remaining (can be negative if expired)
      daysRemaining = Math.ceil(remaining / (1000 * 60 * 60 * 24));
      isExpired = remaining <= 0;

      // Calculate progress percentage (0-100)
      progressPercentage = Math.min(100, Math.max(0, (elapsed / totalDuration) * 100));

      // Calculate detailed time remaining
      if (remaining > 0) {
        const days = Math.floor(remaining / (1000 * 60 * 60 * 24));
        const hours = Math.floor((remaining % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((remaining % (1000 * 60 * 60)) / (1000 * 60));

        timeRemaining = { days, hours, minutes };
      }
    }

    // Override isExpired if status is already expired
    if (status === 'expired') {
      isExpired = true;
    }

    // Status flags for easy conditional rendering
    const isConverted = status === 'converted';
    const isInactive = status === 'inactive';
    const isPaid = isConverted;

    // Determine final status - override if expired
    const finalStatus = isExpired && status === 'active' ? 'expired' : status;

    const trialData: TrialStatusData = {
      accountId,
      status: finalStatus,
      startedAt,
      endsAt,
      daysRemaining,
      isExpired,
      isActive: finalStatus === 'active' && !isExpired,
      isConverted,
      isPaid,
      isInactive,
      progressPercentage,
      timeRemaining,
    };

    logger.info({ accountId, trialStatus: finalStatus }, 'Successfully retrieved trial status from subscriptions');
    return trialData;

  } catch (error) {
    logger.error({ error, accountId }, 'Error getting trial status from subscriptions');
    return null;
  }
}

export async function startTrial(accountId: string, trialDays: number = 7): Promise<{ success: boolean; error?: string }> {
  const logger = await getLogger();
  const client = getSupabaseServerClient();

  try {
    logger.info({ accountId, trialDays }, 'Starting trial for account');

    // First, we need to get or create a billing customer
    const { data: existingCustomer, error: customerQueryError } = await client
      .from('billing_customers')
      .select('id')
      .eq('account_id', accountId)
      .single();

    let billingCustomerId: number;

    if (customerQueryError || !existingCustomer) {
      // Create a new billing customer
      const { data: newCustomer, error: createCustomerError } = await client
        .from('billing_customers')
        .insert({
          account_id: accountId,
          provider: 'stripe',
          customer_id: `trial_${Date.now()}`,
        })
        .select('id')
        .single();

      if (createCustomerError) {
        logger.error({ error: createCustomerError, accountId }, 'Failed to create billing customer');
        return { success: false, error: 'Failed to create billing customer' };
      }

      billingCustomerId = newCustomer.id;
    } else {
      billingCustomerId = existingCustomer.id;
    }

    // Create a trial subscription
    const trialStartDate = new Date();
    const trialEndDate = new Date();
    trialEndDate.setDate(trialEndDate.getDate() + trialDays);

    const { data: subscription, error } = await client
      .from('subscriptions')
      .insert({
        id: `trial_${Date.now()}`,
        account_id: accountId,
        billing_customer_id: billingCustomerId,
        billing_provider: 'stripe',
        status: 'trialing',
        active: true,
        trial_starts_at: trialStartDate.toISOString(),
        trial_ends_at: trialEndDate.toISOString(),
        period_starts_at: trialStartDate.toISOString(),
        period_ends_at: trialEndDate.toISOString(),
        currency: 'usd',
        cancel_at_period_end: false,
      })
      .select()
      .single();

    if (error) {
      logger.error({ error, accountId }, 'Failed to create trial subscription');
      return { success: false, error: error.message };
    }

    logger.info({ accountId, subscriptionId: subscription.id }, 'Successfully started trial');
    return { success: true };

  } catch (error) {
    logger.error({ error, accountId }, 'Error starting trial');
    return { success: false, error: 'Failed to start trial' };
  }
}

export async function convertTrial(accountId: string): Promise<{ success: boolean; error?: string }> {
  const logger = await getLogger();
  const client = getSupabaseServerClient();

  try {
    logger.info({ accountId }, 'Converting trial to paid subscription');

    // Update the trial subscription to active status
    const { error } = await client
      .from('subscriptions')
      .update({
        status: 'active',
        active: true,
      })
      .eq('account_id', accountId)
      .eq('status', 'trialing');

    if (error) {
      logger.error({ error, accountId }, 'Failed to convert trial subscription');
      return { success: false, error: error.message };
    }

    logger.info({ accountId }, 'Successfully converted trial to paid subscription');
    return { success: true };

  } catch (error) {
    logger.error({ error, accountId }, 'Error converting trial');
    return { success: false, error: 'Failed to convert trial' };
  }
} 